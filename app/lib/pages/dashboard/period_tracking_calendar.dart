import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:auto_route/annotations.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../custom_widgets/dotted_border_widget.dart';
import 'package:account_management/account_management.dart';
import '../../helpers.dart';

@RoutePage()
class PeriodTrackingCalendarPage extends StatelessWidget {
  const PeriodTrackingCalendarPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ManagePeriodTrackingBloc>(
            create: (context) => getIt<ManagePeriodTrackingBloc>()),
        BlocProvider<PeriodTrackingWatcherBloc>(
          create: (context) => getIt<PeriodTrackingWatcherBloc>()
            ..add(const PeriodTrackingWatcherEvent.watchAllStarted()),
        ),
      ],
      child: PeriodTrackingCalendarScaffold(),
    );
  }
}

class PeriodTrackingCalendarScaffold extends StatefulWidget {
  const PeriodTrackingCalendarScaffold({super.key});

  @override
  State<PeriodTrackingCalendarScaffold> createState() =>
      _PeriodTrackingCalendarScaffoldState();
}

class _PeriodTrackingCalendarScaffoldState
    extends State<PeriodTrackingCalendarScaffold> {
  bool _isEditMode = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
        listener: (context, state) {
      state.maybeWhen(
        periodTrackingAdded: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Period Tracking Added Successfully')),
          );
          Navigator.pop(context);
        },
        periodTrackingUpdated: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Period Tracking Updated Successfully')),
          );
          Navigator.pop(context);
        },
        periodTrackingDeleted: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Period Tracking Deleted Successfully')),
          );
          Navigator.pop(context);
        },
        dataLoaded: () {
          // Period dates saved successfully - don't navigate away, just stay on current page
        },
        periodTrackingFailure: (failure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Action Failed: ${failure}')),
          );
          // Don't navigate away - let user stay on the page to retry
        },
        orElse: () {},
      );
    }, child:
            BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
                builder: (context, state) {
      return state.map(
        loading: (_) => Scaffold(
          appBar: CurvedAppBar(
            appBarColor: AppTheme.primaryColor,
            logoColor: Color(0xffFAF2DF),
            height: .35.sw,
            topLeftIcon: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                height: 40,
                width: 40,
                child: Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            topRightIcon: Container(
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                color: Color(0xffFAF2DF),
                shape: BoxShape.circle,
              ),
              child: Padding(
                padding: const EdgeInsets.all(6.0),
                child: Icon(
                  Icons.notifications_rounded,
                  color: Color(0xff30285D),
                ),
              ),
            ), // The height of your curved app bar
          ),
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        data: (state) {
          return Scaffold(
            appBar: CurvedAppBar(
              appBarColor: AppTheme.primaryColor,
              logoColor: Color(0xffFAF2DF),
              height: .35.sw,
              topLeftIcon: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  height: 40,
                  width: 40,
                  child: Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              topRightIcon: Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(
                  color: Color(0xffFAF2DF),
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: Icon(
                    Icons.notifications_rounded,
                    color: Color(0xff30285D),
                  ),
                ),
              ), // The height of your curved app bar
            ),
            body: Column(
              children: [
                // Header with title
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                  child: Text(
                    'Insights',
                    style: GoogleFonts.roboto(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 24,
                    ),
                  ),
                ),
                // Scrollable calendar months
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        // Generate multiple months (18 months total - 12 back + 6 forward)
                        ...List.generate(18, (index) {
                          final monthDate = DateTime.now()
                              .subtract(Duration(days: 365))
                              .add(Duration(days: 30 * index));
                          return _buildMonthCalendar(state, monthDate);
                        }),
                        SizedBox(height: 100), // Space for the bottom button
                      ],
                    ),
                  ),
                ),
                // Bottom Edit button
                Container(
                  padding: EdgeInsets.all(20),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        if (_isEditMode) {
                          // Save and exit edit mode
                          context.read<ManagePeriodTrackingBloc>().add(
                                const ManagePeriodTrackingEvent
                                    .savePeriodDatesAndRecalculate(),
                              );
                          _isEditMode = false;

                          // Show success message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'Period dates saved and future dates calculated!'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        } else {
                          // Enter edit mode
                          _isEditMode = true;

                          // Show instruction message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'Select/deselect your period dates. Tap the button again to save.'),
                              backgroundColor: AppTheme.primaryColor,
                              duration: Duration(seconds: 3),
                            ),
                          );
                        }
                      });
                    },
                    child: Container(
                      height: 50,
                      width: 120,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Center(
                        child: Text(
                          _isEditMode ? 'Done' : 'Edit',
                          style: GoogleFonts.roboto(
                            color: Color(0xffFAF2DF),
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }));
  }

  Widget _buildMonthCalendar(dynamic state, DateTime monthDate) {
    return Container(
      margin: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        boxShadow: [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
        border: _isEditMode
            ? Border.all(
                color: AppTheme.primaryColor,
                width: 2,
              )
            : null,
        borderRadius: BorderRadius.all(Radius.circular(20)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            // Month header
            Text(
              DateFormat('MMMM yyyy').format(monthDate),
              style: GoogleFonts.roboto(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 18,
              ),
            ),
            SizedBox(height: 10),
            // Calendar for this month
            TableCalendar<PeriodTrackingModel>(
              firstDay: DateTime(monthDate.year, monthDate.month, 1),
              lastDay: DateTime(monthDate.year, monthDate.month + 1, 0),
              focusedDay: monthDate,
              calendarFormat: CalendarFormat.month,
              headerVisible: false, // Hide header since we have our own
              startingDayOfWeek: StartingDayOfWeek.monday,
              selectedDayPredicate: (day) {
                // Check if this day is the currently focused day (selected for symptom tracking)
                return day.year == state.focusedDay.year &&
                    day.month == state.focusedDay.month &&
                    day.day == state.focusedDay.day;
              },
              onDaySelected: (selectedDay, focusedDay) {
                print('Edit mode: $_isEditMode, Selected day: $selectedDay');

                if (_isEditMode) {
                  // Edit mode - allow period date selection/deselection
                  context.read<ManagePeriodTrackingBloc>().add(
                      ManagePeriodTrackingEvent.selectDay(selectedDay, true));
                } else {
                  // View mode - only allow symptom tracking for today and past dates
                  final today = DateTime.now();
                  final selectedDateOnly = DateTime(
                      selectedDay.year, selectedDay.month, selectedDay.day);
                  final todayOnly =
                      DateTime(today.year, today.month, today.day);

                  if (selectedDateOnly.isAfter(todayOnly)) {
                    // Future date - show message and don't allow selection
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Cannot add symptoms for future dates'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                    return;
                  }

                  // Valid date for symptom tracking - just update focused day
                  context.read<ManagePeriodTrackingBloc>().add(
                      ManagePeriodTrackingEvent.selectDay(selectedDay, false));
                }
              },
              onPageChanged: (focusedDay) {
                // Update focused day when user swipes to different month
                setState(() {
                  _focusedDay = focusedDay;
                });
                context.read<ManagePeriodTrackingBloc>().add(
                    ManagePeriodTrackingEvent.selectDay(focusedDay,
                        false)); // false means just update focus, don't toggle selection
              },
              daysOfWeekHeight: 40,
              calendarStyle: CalendarStyle(
                todayTextStyle: const TextStyle(
                  color: Color(0xff71456F),
                  fontWeight: FontWeight.w400,
                  fontSize: 13,
                ),
                outsideDaysVisible: false,
                todayDecoration: BoxDecoration(
                  border: Border.all(color: AppTheme.primaryColor),
                  shape: BoxShape.circle,
                ),
                defaultTextStyle: const TextStyle(
                  color: Color(0xff71456F),
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                ),
                selectedDecoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: AppTheme.primaryColor),
                ),
                disabledTextStyle: const TextStyle(color: Colors.transparent),
                weekendTextStyle: const TextStyle(
                  color: Color(0xff71456F),
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                ),
                selectedTextStyle: GoogleFonts.roboto(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                ),
              ),
              calendarBuilders: CalendarBuilders(
                defaultBuilder: (context, date, focusedDay) {
                  // Check if this is the focused day (selected for symptom tracking)
                  bool isFocusedDay = date.year == state.focusedDay.year &&
                      date.month == state.focusedDay.month &&
                      date.day == state.focusedDay.day;

                  // Check if this is today
                  final today = DateTime.now();
                  bool isToday = date.year == today.year &&
                      date.month == today.month &&
                      date.day == today.day;

                  // Check if this is a selected period date
                  bool isSelectedPeriodDate =
                      (state.selectedDays as List<DateTime>).any(
                          (DateTime selectedDay) =>
                              selectedDay.year == date.year &&
                              selectedDay.month == date.month &&
                              selectedDay.day == date.day);

                  // Check if this is an ovulation date
                  bool isOvulationDate = (state.ovulationDays as List<DateTime>)
                      .any((DateTime ovulationDay) =>
                          ovulationDay.year == date.year &&
                          ovulationDay.month == date.month &&
                          ovulationDay.day == date.day);

                  // Check if this is a future date
                  bool isFutureDate = date
                      .isAfter(DateTime(today.year, today.month, today.day));

                  // Priority: Focused day > Today > Selected period > Ovulation > Default
                  if (isFocusedDay) {
                    return Container(
                      margin: const EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${date.day}',
                          style: GoogleFonts.roboto(
                            color: const Color(0xffFBF0D5),
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    );
                  } else if (isToday) {
                    // Today's date when it's not the focused day - distinctive styling
                    return Container(
                      margin: const EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: const Color(0xffECA83D)
                            .withValues(alpha: 0.2), // Orange background
                        border: Border.all(
                          color: const Color(0xffECA83D), // Orange border
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          '${date.day}',
                          style: GoogleFonts.roboto(
                            color: const Color(0xffECA83D), // Orange text
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    );
                  } else if (isSelectedPeriodDate) {
                    // Period dates - solid border for past, dotted for future
                    if (isFutureDate) {
                      return Container(
                        margin: const EdgeInsets.all(4.0),
                        child: DottedBorder(
                          color: AppTheme.primaryColor,
                          strokeWidth: 2,
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color:
                                  AppTheme.primaryColor.withValues(alpha: 0.1),
                            ),
                            child: Center(
                              child: Text(
                                '${date.day}',
                                style: GoogleFonts.roboto(
                                  color: AppTheme.primaryColor,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    } else {
                      return Container(
                        margin: const EdgeInsets.all(4.0),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppTheme.primaryColor,
                            width: 2,
                          ),
                          color: AppTheme.primaryColor.withValues(alpha: 0.2),
                        ),
                        child: Center(
                          child: Text(
                            '${date.day}',
                            style: GoogleFonts.roboto(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      );
                    }
                  } else if (isOvulationDate) {
                    return Container(
                      margin: const EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Color(0xffECA83D).withValues(alpha: 0.3),
                        border: Border.all(
                          color: Color(0xffECA83D),
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          '${date.day}',
                          style: GoogleFonts.roboto(
                            color: Color(0xffECA83D),
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    );
                  } else {
                    return Center(
                      child: Text(
                        '${date.day}',
                        style: GoogleFonts.roboto(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w400,
                          fontSize: 16,
                        ),
                      ),
                    );
                  }
                },
                selectedBuilder: (context, date, focusedDay) {
                  // The selected day (focused day) should have solid purple background
                  return Container(
                    margin: const EdgeInsets.all(4.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppTheme.primaryColor,
                    ),
                    child: Center(
                      child: Text(
                        '${date.day}',
                        style: GoogleFonts.roboto(
                          color: const Color(0xffFBF0D5),
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  );
                },
                todayBuilder: (context, date, focusedDay) {
                  bool isFocusedDay = date.year == state.focusedDay.year &&
                      date.month == state.focusedDay.month &&
                      date.day == state.focusedDay.day;

                  if (isFocusedDay) {
                    // Today is the focused day - show purple background
                    return Container(
                      margin: const EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${date.day}',
                          style: GoogleFonts.roboto(
                            color: const Color(0xffFBF0D5),
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    );
                  } else {
                    // Today is NOT the focused day - show distinctive orange styling
                    return Container(
                      margin: const EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: const Color(0xffECA83D)
                            .withValues(alpha: 0.2), // Orange background
                        border: Border.all(
                          color: const Color(0xffECA83D), // Orange border
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          '${date.day}',
                          style: GoogleFonts.roboto(
                            color: const Color(0xffECA83D), // Orange text
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
